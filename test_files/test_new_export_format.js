// Test the new JSON export format with boxId, pageIndex, coordinates, and normalizedCoordinates

// Mock PDF and canvas dimensions
const mockPdfDimensions = { width: 612, height: 792 }
const mockCanvasDimensions = { width: 1224, height: 1584 }

// Simulate the canvasToPdfCoordinates function
const canvasToPdfCoordinates = (canvasX, canvasY) => {
  const scaleX = mockPdfDimensions.width / mockCanvasDimensions.width
  const scaleY = mockPdfDimensions.height / mockCanvasDimensions.height
  
  // Convert canvas coordinates (top-left origin) to PDF coordinates (bottom-left origin)
  const pdfX = canvasX * scaleX
  const pdfY = mockPdfDimensions.height - (canvasY * scaleY)
  
  return { x: pdfX, y: pdfY }
}

// Test annotations
const testAnnotations = [
  {
    id: 'rect-001',
    type: 'rectangle',
    x: 200,      // Canvas coordinates
    y: 300,
    width: 400,
    height: 200,
    pageIndex: 0,
    color: '#ff0000',
    label: 'Living Room',
    roomName: 'Living Room',
    roomPath: ['Ground Floor', 'Living Room']
  },
  {
    id: 'poly-001',
    type: 'polygon',
    points: [
      { x: 600, y: 400 },
      { x: 800, y: 450 },
      { x: 750, y: 600 },
      { x: 550, y: 550 }
    ],
    pageIndex: 0,
    color: '#00ff00',
    label: 'Kitchen',
    roomName: 'Kitchen',
    roomPath: ['Ground Floor', 'Kitchen']
  }
]

// Function to export annotation in new format
const exportAnnotationNewFormat = (annotation) => {
  if (annotation.type === 'rectangle') {
    // Calculate all four corner points in PDF coordinates
    const pdfTopLeft = canvasToPdfCoordinates(annotation.x, annotation.y)
    const pdfTopRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y)
    const pdfBottomLeft = canvasToPdfCoordinates(annotation.x, annotation.y + annotation.height)
    const pdfBottomRight = canvasToPdfCoordinates(annotation.x + annotation.width, annotation.y + annotation.height)

    // Calculate normalized coordinates for all four corner points (0-1 range)
    const pdfDimensions = mockPdfDimensions
    const normalizedTopLeft = {
      x: pdfTopLeft.x / pdfDimensions.width,
      y: pdfTopLeft.y / pdfDimensions.height
    }
    const normalizedTopRight = {
      x: pdfTopRight.x / pdfDimensions.width,
      y: pdfTopRight.y / pdfDimensions.height
    }
    const normalizedBottomLeft = {
      x: pdfBottomLeft.x / pdfDimensions.width,
      y: pdfBottomLeft.y / pdfDimensions.height
    }
    const normalizedBottomRight = {
      x: pdfBottomRight.x / pdfDimensions.width,
      y: pdfBottomRight.y / pdfDimensions.height
    }

    return {
      boxId: annotation.id,
      pageIndex: annotation.pageIndex,
      coordinates: {
        // All four corner points in PDF coordinates (absolute)
        topLeft: pdfTopLeft,
        topRight: pdfTopRight,
        bottomLeft: pdfBottomLeft,
        bottomRight: pdfBottomRight
      },
      normalizedCoordinates: {
        // All four corner points in normalized coordinates (0-1 range)
        topLeft: normalizedTopLeft,
        topRight: normalizedTopRight,
        bottomLeft: normalizedBottomLeft,
        bottomRight: normalizedBottomRight
      },
      type: annotation.type,
      color: annotation.color,
      label: annotation.label,
      roomName: annotation.roomName,
      roomPath: annotation.roomPath
    }
  } else if (annotation.type === 'polygon') {
    // Convert all polygon points to PDF coordinates
    const pdfPoints = annotation.points.map(point => canvasToPdfCoordinates(point.x, point.y))

    // Calculate normalized points (0-1 range) for portability
    const pdfDimensions = mockPdfDimensions
    const normalizedPoints = pdfPoints.map(point => ({
      x: point.x / pdfDimensions.width,
      y: point.y / pdfDimensions.height
    }))

    return {
      boxId: annotation.id,
      pageIndex: annotation.pageIndex,
      coordinates: pdfPoints,
      normalizedCoordinates: normalizedPoints,
      type: annotation.type,
      color: annotation.color,
      label: annotation.label,
      roomName: annotation.roomName,
      roomPath: annotation.roomPath
    }
  }
}

// Test the new export format
console.log("🧪 Testing New JSON Export Format")
console.log("=" .repeat(60))
console.log(`PDF Dimensions: ${mockPdfDimensions.width} x ${mockPdfDimensions.height} points`)
console.log(`Canvas Dimensions: ${mockCanvasDimensions.width} x ${mockCanvasDimensions.height} pixels`)
console.log("")

const exportData = {
  pdfName: "test_floor_plan.pdf",
  pdfDimensions: mockPdfDimensions,
  annotations: testAnnotations.map(exportAnnotationNewFormat)
}

console.log("📊 EXPORTED JSON STRUCTURE:")
console.log("=" .repeat(50))
console.log(JSON.stringify(exportData, null, 2))

console.log("\n✅ VALIDATION CHECKS:")
console.log("=" .repeat(50))

exportData.annotations.forEach((annotation, index) => {
  console.log(`\nAnnotation ${index + 1} (${annotation.type}):`)
  console.log(`  ✓ Box ID: ${annotation.boxId}`)
  console.log(`  ✓ Page Index: ${annotation.pageIndex}`)
  
  if (annotation.type === 'rectangle') {
    console.log(`  ✓ Coordinates (4 points):`)
    console.log(`    - Top Left: (${annotation.coordinates.topLeft.x.toFixed(2)}, ${annotation.coordinates.topLeft.y.toFixed(2)})`)
    console.log(`    - Top Right: (${annotation.coordinates.topRight.x.toFixed(2)}, ${annotation.coordinates.topRight.y.toFixed(2)})`)
    console.log(`    - Bottom Left: (${annotation.coordinates.bottomLeft.x.toFixed(2)}, ${annotation.coordinates.bottomLeft.y.toFixed(2)})`)
    console.log(`    - Bottom Right: (${annotation.coordinates.bottomRight.x.toFixed(2)}, ${annotation.coordinates.bottomRight.y.toFixed(2)})`)
    
    console.log(`  ✓ Normalized Coordinates (4 points):`)
    console.log(`    - Top Left: (${annotation.normalizedCoordinates.topLeft.x.toFixed(3)}, ${annotation.normalizedCoordinates.topLeft.y.toFixed(3)})`)
    console.log(`    - Top Right: (${annotation.normalizedCoordinates.topRight.x.toFixed(3)}, ${annotation.normalizedCoordinates.topRight.y.toFixed(3)})`)
    console.log(`    - Bottom Left: (${annotation.normalizedCoordinates.bottomLeft.x.toFixed(3)}, ${annotation.normalizedCoordinates.bottomLeft.y.toFixed(3)})`)
    console.log(`    - Bottom Right: (${annotation.normalizedCoordinates.bottomRight.x.toFixed(3)}, ${annotation.normalizedCoordinates.bottomRight.y.toFixed(3)})`)
  } else if (annotation.type === 'polygon') {
    console.log(`  ✓ Coordinates (${annotation.coordinates.length} points):`)
    annotation.coordinates.forEach((point, i) => {
      console.log(`    - Point ${i + 1}: (${point.x.toFixed(2)}, ${point.y.toFixed(2)})`)
    })
    
    console.log(`  ✓ Normalized Coordinates (${annotation.normalizedCoordinates.length} points):`)
    annotation.normalizedCoordinates.forEach((point, i) => {
      console.log(`    - Point ${i + 1}: (${point.x.toFixed(3)}, ${point.y.toFixed(3)})`)
    })
  }
  
  console.log(`  ✓ Type: ${annotation.type}`)
  console.log(`  ✓ Color: ${annotation.color}`)
  console.log(`  ✓ Label: ${annotation.label}`)
  console.log(`  ✓ Room Name: ${annotation.roomName}`)
  console.log(`  ✓ Room Path: ${annotation.roomPath.join(' → ')}`)
})

console.log("\n🎯 FORMAT SUMMARY:")
console.log("=" .repeat(50))
console.log("✅ Each annotation now includes:")
console.log("   • boxId (unique identifier)")
console.log("   • pageIndex (which page the annotation is on)")
console.log("   • coordinates (all corner points for rectangles, all points for polygons)")
console.log("   • normalizedCoordinates (same structure but in 0-1 range)")
console.log("   • type, color, label, roomName, roomPath")
console.log("\n✅ Rectangle coordinates include all 4 corner points:")
console.log("   • topLeft, topRight, bottomLeft, bottomRight")
console.log("\n✅ Polygon coordinates include all vertex points")
console.log("\n✅ Normalized coordinates are in 0-1 range for portability")
