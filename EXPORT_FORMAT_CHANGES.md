# JSON Export Format Changes

## Summary
Updated the JSON export structure to provide cleaner, more comprehensive coordinate information for each annotation box.

## Changes Made

### 1. Modified Export Structure
**File:** `src/components/ExportUtils.jsx`

#### Before (Old Format):
```json
{
  "id": "unique-uuid",
  "type": "rectangle", 
  "pageIndex": 0,
  "coordinates": {
    "pdf": { "x": 100, "y": 200, "width": 150, "height": 100 },
    "normalized": { "x": 0.163, "y": 0.253, "width": 0.245, "height": 0.126 },
    "x": 100, "y": 200, "width": 150, "height": 100,
    "points": {
      "topLeft": {"x": 100, "y": 200},
      "topRight": {"x": 250, "y": 200},
      "bottomLeft": {"x": 100, "y": 300},
      "bottomRight": {"x": 250, "y": 300}
    }
  }
}
```

#### After (New Format):
```json
{
  "boxId": "unique-uuid",
  "pageIndex": 0,
  "coordinates": {
    "topLeft": {"x": 100, "y": 200},
    "topRight": {"x": 250, "y": 200},
    "bottomLeft": {"x": 100, "y": 300},
    "bottomRight": {"x": 250, "y": 300}
  },
  "normalizedCoordinates": {
    "topLeft": {"x": 0.163, "y": 0.253},
    "topRight": {"x": 0.408, "y": 0.253},
    "bottomLeft": {"x": 0.163, "y": 0.379},
    "bottomRight": {"x": 0.408, "y": 0.379}
  },
  "type": "rectangle",
  "color": "#ff0000",
  "label": "Room A",
  "roomName": "Living Room",
  "roomPath": ["Ground Floor", "Living Room"]
}
```

### 2. Key Improvements

#### For Rectangles:
- **boxId**: Cleaner naming (was `id`)
- **coordinates**: All 4 corner points in PDF coordinates (absolute)
- **normalizedCoordinates**: All 4 corner points in 0-1 range
- Removed redundant nested structure and legacy fields

#### For Polygons:
- **boxId**: Cleaner naming (was `id`)
- **coordinates**: Array of all vertex points in PDF coordinates
- **normalizedCoordinates**: Array of all vertex points in 0-1 range
- Simplified structure without nested objects

### 3. Benefits

1. **Cleaner Structure**: Removed redundant nested objects and legacy fields
2. **Complete Coordinate Information**: All corner points for rectangles, all vertices for polygons
3. **Dual Coordinate Systems**: Both absolute PDF coordinates and normalized (0-1) coordinates
4. **Better Naming**: `boxId` instead of `id` for clarity
5. **Consistent Format**: Same structure pattern for both rectangles and polygons

### 4. Updated Documentation
**File:** `README.md`
- Updated the example JSON export format to reflect the new structure
- Added examples showing both rectangle and polygon formats
- Included normalized coordinates examples

### 5. Testing
**File:** `test_files/test_new_export_format.js`
- Created comprehensive test to verify the new export format
- Tests both rectangle and polygon annotations
- Validates coordinate conversion and normalization
- Confirms all required fields are present

## Usage

The export functionality remains the same from the user perspective:
- Click the 💾 button in the toolbar to export JSON
- Use Ctrl+S keyboard shortcut
- The exported file will have the new format automatically

## Backward Compatibility

This is a breaking change to the export format. If you have existing JSON files from the old format, they will need to be converted to work with any import functionality that might be added in the future.

## Example Output

```json
{
  "pdfName": "floor_plan.pdf",
  "pdfDimensions": {
    "width": 612,
    "height": 792
  },
  "annotations": [
    {
      "boxId": "rect-001",
      "pageIndex": 0,
      "coordinates": {
        "topLeft": {"x": 100, "y": 642},
        "topRight": {"x": 300, "y": 642},
        "bottomLeft": {"x": 100, "y": 542},
        "bottomRight": {"x": 300, "y": 542}
      },
      "normalizedCoordinates": {
        "topLeft": {"x": 0.163, "y": 0.811},
        "topRight": {"x": 0.490, "y": 0.811},
        "bottomLeft": {"x": 0.163, "y": 0.684},
        "bottomRight": {"x": 0.490, "y": 0.684}
      },
      "type": "rectangle",
      "color": "#ff0000",
      "label": "Living Room",
      "roomName": "Living Room",
      "roomPath": ["Ground Floor", "Living Room"]
    }
  ]
}
```

## Files Modified

1. `src/components/ExportUtils.jsx` - Updated export logic
2. `README.md` - Updated documentation with new format
3. `test_files/test_new_export_format.js` - Added comprehensive test
4. `EXPORT_FORMAT_CHANGES.md` - This documentation file
